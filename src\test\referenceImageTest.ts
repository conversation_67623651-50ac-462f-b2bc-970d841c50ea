// 根据用户提供的参考图片创建的测试用例
import { YaoType } from '../components/YaoLine';
import { generateHexagram } from '../components/HexagramUtils';

// 根据参考图片的本卦（泽天夫）创建爻线
// 从下到上：
// 初爻：财子水（少阳，静爻）- yang
// 二爻：官寅木（老阳，变爻）- yang_changing  
// 三爻：财亥水（少阳，静爻）- yang
// 四爻：兄辰土（少阳，静爻）- yang
// 五爻：官寅木（老阳，变爻）- yang_changing
// 六爻：财子水（老阴，变爻）- yin_changing
const referenceLines: YaoType[] = [
  'yang',         // 初爻：少阳（静爻）
  'yang_changing', // 二爻：老阳（变爻）
  'yang',         // 三爻：少阳（静爻）
  'yang',         // 四爻：少阳（静爻）
  'yang_changing', // 五爻：老阳（变爻）
  'yin_changing'  // 六爻：老阴（变爻）
];

console.log('=== 参考图片测试用例 ===');
console.log('爻线配置:', referenceLines);

// 生成完整卦象
const hexagram = generateHexagram(referenceLines);

console.log('本卦名称:', hexagram.name);
console.log('本卦世应位置:', { world: hexagram.worldPosition, response: hexagram.responsePosition });
console.log('本卦六亲:', hexagram.sixRelations);
console.log('本卦六神:', hexagram.sixSpirits);

if (hexagram.changedHexagram) {
  console.log('\n变卦名称:', hexagram.changedHexagram.name);
  console.log('变卦世应位置:', { world: hexagram.changedHexagram.worldPosition, response: hexagram.changedHexagram.responsePosition });
  console.log('变卦六亲:', hexagram.changedHexagram.sixRelations);
  console.log('变卦六神:', hexagram.changedHexagram.sixSpirits);
  
  // 验证变卦的世应位置是否和本卦一致
  const worldPositionMatch = hexagram.worldPosition === hexagram.changedHexagram.worldPosition;
  const responsePositionMatch = hexagram.responsePosition === hexagram.changedHexagram.responsePosition;
  
  console.log('\n世应位置验证:');
  console.log('世位置一致:', worldPositionMatch);
  console.log('应位置一致:', responsePositionMatch);
  
  if (!worldPositionMatch || !responsePositionMatch) {
    console.error('❌ 变卦的世应位置与本卦不一致！');
  } else {
    console.log('✅ 变卦的世应位置与本卦一致');
  }
}

// 验证爻线类型
console.log('\n爻线类型验证:');
referenceLines.forEach((line, index) => {
  const position = index + 1;
  const isChanging = line.includes('changing');
  const isYang = line.startsWith('yang');
  
  console.log(`第${position}爻: ${line} -> ${isYang ? '阳爻' : '阴爻'} ${isChanging ? '(变爻)' : '(静爻)'}`);
});

export { referenceLines, hexagram };
