import { YaoType } from '@/components/YaoLine';
import {
  YAOS, GANS, ZHIS, XING5, QING6, SHEN6, ZHI5, GUA5,
  NAJIA, KONG, GUA64, GUAS, LIUHE, PROPERTY_PAIRS, EARTH_ENUM_MAP
} from '@/constants/najia';
import {
  HEXAGRAM_BINARY_MAP,
  HEXAGRAM_NAME_TO_NUMBER,
  HEAVENLY_STEMS,
  EARTHLY_BRANCHES
} from '@/constants/hexagram';

// 计算卦象名称和编号
export const calculateHexagram = (lines: YaoType[]): { name: string; number: number } => {
  // 将爻线转换为二进制字符串（自下而上）
  const binaryString = lines.map(line => {
    const isYang = line === 'yang' || line === 'yang_changing';
    return isYang ? '1' : '0';
  }).join('');

  const name = GUA64[binaryString] || '乾为天';
  const number = HEXAGRAM_NAME_TO_NUMBER[name] || 1;

  return { name, number };
};

// 计算六神（根据日干）
export const calculateSixSpirits = (dayStem: string): string[] => {
  const gm = GANS.indexOf(dayStem as any);
  if (gm === -1) return [...SHEN6]; // 默认从青龙开始

  // 六神起法：甲乙起青龙，丙丁起朱雀，戊起勾陈，己起螣蛇，庚辛起白虎，壬癸起玄武
  let num = Math.ceil((gm + 1) / 2) - 7;

  if (gm === 4) num = -4; // 戊
  if (gm === 5) num = -3; // 己
  if (gm > 5) num += 1;

  // 调整为正数索引
  const startIndex = ((num % 6) + 6) % 6;

  return [...SHEN6.slice(startIndex), ...SHEN6.slice(0, startIndex)];
};

// 获取纳甲配置（根据卦的二进制码）
export const getNajia = (symbol: string): string[] => {
  const wai = symbol.slice(3); // 外卦
  const nei = symbol.slice(0, 3); // 内卦

  // 将二进制转换为八卦索引
  const waiIndex = parseInt(wai, 2);
  const neiIndex = parseInt(nei, 2);

  // 获取内卦纳甲配置
  const gan = NAJIA[neiIndex][0][0];
  const ngz = [
    gan + NAJIA[neiIndex][0][1],
    gan + NAJIA[neiIndex][0][2],
    gan + NAJIA[neiIndex][0][3]
  ];

  // 获取外卦纳甲配置
  const waiGan = NAJIA[waiIndex][1][0];
  const wgz = [
    waiGan + NAJIA[waiIndex][1][1],
    waiGan + NAJIA[waiIndex][1][2],
    waiGan + NAJIA[waiIndex][1][3]
  ];

  return [...ngz, ...wgz];
};

// 根据卦宫五行和地支查找六亲关系
export const findReps = (property: number, earthEnum: string): string => {
  const pair = [property, earthEnum];

  // 检查每个六亲类型
  for (const [repType, pairs] of Object.entries(PROPERTY_PAIRS)) {
    for (const [prop, earth] of pairs) {
      if (prop === property && earth === earthEnum) {
        switch (repType) {
          case 'GUAN': return '官鬼';
          case 'QI': return '妻财';
          case 'XIONG': return '兄弟';
          case 'FU': return '父母';
          case 'ZI': return '子孙';
        }
      }
    }
  }

  return '子孙'; // 默认返回子孙
};

// 获取卦宫（基于传统认宫诀）
export const seekForOrigin = (lines: YaoType[]): number => {
  const binaryString = lines.map(line => {
    const isYang = line === 'yang' || line === 'yang_changing';
    return isYang ? '1' : '0';
  }).join('');

  // 检查是否为八纯卦
  const innerGua = binaryString.slice(0, 3);
  const outerGua = binaryString.slice(3, 6);

  if (innerGua === outerGua) {
    // 八纯卦，直接返回对应卦宫
    return parseInt(innerGua, 2);
  }

  // 使用传统认宫诀
  const { worldPosition } = calculateWorldAndResponse(lines);
  return palace(binaryString, worldPosition);
};

// 计算六亲关系（基于qichenx项目的正确逻辑）
export const calculateSixRelations = (hexagramNumber: number, lines: YaoType[]): string[] => {
  // 1. 获取纳甲配置
  const binaryString = lines.map(line => {
    const isYang = line === 'yang' || line === 'yang_changing';
    return isYang ? '1' : '0';
  }).join('');

  const najiaGanZhi = getNajia(binaryString);

  // 2. 获取卦宫
  const origin = seekForOrigin(lines);

  // 3. 确定卦宫五行属性（按NAJIA数组索引）
  // 坤艮坎巽震离兑乾 (000,001,010,011,100,101,110,111)
  // 对应索引: 0,  1,  2,  3,  4,  5,  6,  7
  let property = 5; // 默认值
  // 乾、兑属金 (7, 6)
  if ([7, 6].includes(origin)) {
    property = 0;
  }
  // 坎属水 (2)
  else if ([2].includes(origin)) {
    property = 1;
  }
  // 坤、艮属土 (0, 1)
  else if ([0, 1].includes(origin)) {
    property = 2;
  }
  // 震、巽属木 (4, 3)
  else if ([4, 3].includes(origin)) {
    property = 3;
  }
  // 离属火 (5)
  else {
    property = 4;
  }

  const result = [];

  for (let i = 0; i < 6; i++) {
    // 4. 获取地支
    const ganZhi = najiaGanZhi[i];
    const diZhi = ganZhi[1]; // 地支是第二个字符

    // 5. 获取地支枚举
    const earthEnum = EARTH_ENUM_MAP[diZhi as any];

    // 6. 计算六亲
    const liuqin = findReps(property, earthEnum);

    // 7. 获取地支五行
    const diZhiIndex = ZHIS.indexOf(diZhi as any);
    const diZhiWuxing = XING5[ZHI5[diZhiIndex]];

    // 8. 组合结果：六亲+地支+五行
    result.push(`${liuqin}${diZhi}${diZhiWuxing}`);
  }

  return result;
};

// 干支五行计算
export const GZ5X = (gz: string): string => {
  if (gz.length < 2) return gz + '土';

  const zhi = gz[1];
  const zhiIndex = ZHIS.indexOf(zhi as any);

  if (zhiIndex === -1) return gz + '土';

  return gz + XING5[ZHI5[zhiIndex]];
};

// 计算旬空
export const xkong = (gz: string): string => {
  if (gz.length < 2) return '戌亥';

  const gan = gz[0];
  const zhi = gz[1];

  let ganIndex = GANS.indexOf(gan as any);
  let zhiIndex = ZHIS.indexOf(zhi as any);

  if (ganIndex === -1 || zhiIndex === -1) return '戌亥';

  if (ganIndex === zhiIndex || zhiIndex < ganIndex) {
    zhiIndex += 12;
  }

  const xk = Math.floor((zhiIndex - ganIndex) / 2) - 1;

  return KONG[xk] || '戌亥';
};

// 计算干支（使用lunar-javascript库）
export const getStemBranch = async (date: Date) => {
  try {
    // 动态导入lunar-javascript
    const { Solar } = await import('lunar-javascript');

    // 处理晚子时：如果是23时，算作下一天的日干支
    const adjustedDate = new Date(date);
    if (date.getHours() === 23) {
      adjustedDate.setDate(adjustedDate.getDate() + 1);
      // 晚子时设为00:00，因为它属于下一天的子时
      adjustedDate.setHours(0, 0, 0, 0);
    }

    const solar = Solar.fromYmdHms(
      adjustedDate.getFullYear(),
      adjustedDate.getMonth() + 1,
      adjustedDate.getDate(),
      adjustedDate.getHours(),
      adjustedDate.getMinutes(),
      adjustedDate.getSeconds()
    );

    const lunar = solar.getLunar();
    const eightChar = lunar.getEightChar();

    return {
      year: eightChar.getYear() + '年',
      month: eightChar.getMonth() + '月',
      day: eightChar.getDay() + '日',
      hour: eightChar.getTime() + '时'
    };
  } catch (error) {
    console.error('Error calculating stem-branch:', error);
    // 如果lunar库失败，返回基本信息
    return {
      year: '未知年',
      month: '未知月',
      day: '未知日',
      hour: '未知时'
    };
  }
};



// 计算节气
export const getSolarTerm = (date: Date): string => {
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  
  // 2024年节气日期（每年略有差异，这里使用近似值）
  const solarTermDates = [
    [1, 6, '小寒'], [1, 20, '大寒'], [2, 4, '立春'], [2, 19, '雨水'],
    [3, 6, '惊蛰'], [3, 21, '春分'], [4, 5, '清明'], [4, 20, '谷雨'],
    [5, 6, '立夏'], [5, 21, '小满'], [6, 6, '芒种'], [6, 21, '夏至'],
    [7, 7, '小暑'], [7, 23, '大暑'], [8, 8, '立秋'], [8, 23, '处暑'],
    [9, 8, '白露'], [9, 23, '秋分'], [10, 8, '寒露'], [10, 24, '霜降'],
    [11, 8, '立冬'], [11, 22, '小雪'], [12, 7, '大雪'], [12, 22, '冬至']
  ];
  
  // 找到当前最近的节气
  for (let i = solarTermDates.length - 1; i >= 0; i--) {
    const [termMonth, termDay, termName] = solarTermDates[i];
    if (month > (termMonth as number) || (month === (termMonth as number) && day >= (termDay as number))) {
      return termName as string;
    }
  }
  
  return '冬至'; // 默认返回
};

// 计算所有空亡（年月日时）
export const getAllVoidness = async (date: Date) => {
  try {
    // 动态导入lunar-javascript
    const { Solar } = await import('lunar-javascript');

    // 处理晚子时：如果是23时，算作下一天
    const adjustedDate = new Date(date);
    if (date.getHours() === 23) {
      adjustedDate.setDate(adjustedDate.getDate() + 1);
      adjustedDate.setHours(0, 0, 0, 0);
    }

    const solar = Solar.fromYmdHms(
      adjustedDate.getFullYear(),
      adjustedDate.getMonth() + 1,
      adjustedDate.getDate(),
      adjustedDate.getHours(),
      adjustedDate.getMinutes(),
      adjustedDate.getSeconds()
    );

    const lunar = solar.getLunar();
    const eightChar = lunar.getEightChar();

    return {
      year: eightChar.getYearXunKong().split(''),
      month: eightChar.getMonthXunKong().split(''),
      day: eightChar.getDayXunKong().split(''),
      hour: eightChar.getTimeXunKong().split('')
    };
  } catch (error) {
    console.error('Error calculating all voidness:', error);
    // 降级到原来的计算方法
    const stemBranch = await getStemBranch(date);
    const dayGanZhi = stemBranch.day.replace('日', '');
    const voidness = xkong(dayGanZhi);
    return {
      year: voidness,
      month: voidness,
      day: voidness,
      hour: voidness
    };
  }
};

// 计算空亡（使用lunar-javascript库）- 保持向后兼容
export const getVoidness = async (date: Date): Promise<string[]> => {
  try {
    const allVoidness = await getAllVoidness(date);
    return allVoidness.day;
  } catch (error) {
    console.error('Error calculating voidness:', error);
    // 降级到原来的计算方法
    const stemBranch = await getStemBranch(date);
    const dayGanZhi = stemBranch.day.replace('日', '');
    const voidness = xkong(dayGanZhi);
    return [voidness[0], voidness[1]];
  }
};

// 同步版本的干支计算（用于React组件）
export const getStemBranchSync = (date: Date) => {
  // 在浏览器环境中，lunar库的同步版本不可用，返回基本信息
  return {
    year: '计算中年',
    month: '计算中月',
    day: '计算中日',
    hour: '计算中时'
  };
};

// 同步版本的所有空亡计算（用于React组件）
export const getAllVoidnessSync = (date: Date) => {
  // 在浏览器环境中，使用简化计算
  const stemBranch = getStemBranchSync(date);
  const dayGanZhi = stemBranch.day.replace('日', '');
  const voidness = xkong(dayGanZhi);

  // 返回统一的对象结构
  return {
    year: voidness,
    month: voidness,
    day: voidness,
    hour: voidness
  };
};

// 同步版本的空亡计算（用于React组件）
export const getVoidnessSync = (date: Date): string[] => {
  const allVoidness = getAllVoidnessSync(date);
  return Array.isArray(allVoidness.day) ? allVoidness.day : [allVoidness.day[0], allVoidness.day[1]];
};

// 兼容旧的API
export const getVoidnessFromStemBranch = (dayStem: string, dayBranch: string): string[] => {
  const dayGanZhi = dayStem + dayBranch;
  const voidness = xkong(dayGanZhi);
  return [voidness[0], voidness[1]];
};

// 寻世诀算法：天同二世天变五，地同四世地变初，本宫六世三世异，人同游魂人变归
export const setShiYao = (symbol: string): [number, number, number] => {
  const wai = symbol.slice(3); // 外卦
  const nei = symbol.slice(0, 3); // 内卦

  const shiy = (shi: number, index?: number): [number, number, number] => {
    const ying = shi > 3 ? shi - 3 : shi + 3;
    const idx = index !== undefined ? index : shi;
    return [shi, ying, idx];
  };

  // 天同二世天变五
  if (wai[2] === nei[2]) {
    if (wai[1] !== nei[1] && wai[0] !== nei[0]) {
      return shiy(2);
    }
  } else {
    if (wai[1] === nei[1] && wai[0] === nei[0]) {
      return shiy(5);
    }
  }

  // 人同游魂人变归
  if (wai[1] === nei[1]) {
    if (wai[0] !== nei[0] && wai[2] !== nei[2]) {
      return shiy(4, 6); // 游魂
    }
  } else {
    // 归魂问题修复
    if (wai[0] === nei[0] && wai[2] === nei[2]) {
      return shiy(3, 6); // 归魂
    }
  }

  // 地同四世地变初
  if (wai[0] === nei[0]) {
    if (wai[1] !== nei[1] && wai[2] !== nei[2]) {
      return shiy(4);
    }
  } else {
    if (wai[1] === nei[1] && wai[2] === nei[2]) {
      return shiy(1);
    }
  }

  // 本宫六世
  if (wai === nei) {
    return shiy(6);
  }

  // 三世异
  return shiy(3);
};

// 计算世应位置
export const calculateWorldAndResponse = (lines: YaoType[]): { worldPosition: number; responsePosition: number } => {
  const binaryString = lines.map(line => {
    const isYang = line === 'yang' || line === 'yang_changing';
    return isYang ? '1' : '0';
  }).join('');

  const [worldPosition, responsePosition] = setShiYao(binaryString);

  return { worldPosition, responsePosition };
};

// 判断卦宫（认宫诀）
export const palace = (symbol: string, index: number): number => {
  const wai = symbol.slice(3); // 外卦
  const nei = symbol.slice(0, 3); // 内卦
  let hun = '';

  // 判断游魂归魂
  if (wai[1] === nei[1]) {
    if (wai[0] !== nei[0] && wai[2] !== nei[2]) {
      hun = '游魂';
    }
  } else {
    if (wai[0] === nei[0] && wai[2] === nei[2]) {
      hun = '归魂';
    }
  }

  // 归魂内卦是本宫
  if (hun === '归魂') {
    return parseInt(nei, 2);
  }

  // 一二三六外卦宫
  if ([1, 2, 3, 6].includes(index)) {
    return parseInt(wai, 2);
  }

  // 四五游魂内变更
  if ([4, 5].includes(index) || hun === '游魂') {
    const changedNei = nei.split('').map(c => (parseInt(c) ^ 1).toString()).join('');
    return parseInt(changedNei, 2);
  }

  return 0; // 默认乾宫
};

// 判断卦象类型
export const getType = (symbol: string): string => {
  const soulType = soul(symbol);
  if (soulType) return soulType;

  if (attack(symbol)) return '六冲';

  const uniteType = unite(symbol);
  if (uniteType) return uniteType;

  return '';
};

// 判断游魂归魂
const soul = (symbol: string): string => {
  const wai = symbol.slice(3);
  const nei = symbol.slice(0, 3);

  if (wai[1] === nei[1]) {
    if (wai[0] !== nei[0] && wai[2] !== nei[2]) {
      return '游魂';
    }
  } else {
    if (wai[0] === nei[0] && wai[2] === nei[2]) {
      return '归魂';
    }
  }

  return '';
};

// 判断六冲卦
const attack = (symbol: string): boolean => {
  const wai = symbol.slice(3);
  const nei = symbol.slice(0, 3);

  // 内外卦相同
  if (wai === nei) return true;

  // 天雷无妄和雷天大壮
  const gua = [nei, wai];
  try {
    const set1 = new Set(gua);
    const set2 = new Set(['100', '111']);
    const diff = new Set([...set1].filter(x => !set2.has(x)));
    if (diff.size === 0) return true;
  } catch {
    // Set.difference 可能不支持，使用传统方法
    const set1 = new Set(gua);
    const set2 = new Set(['100', '111']);
    const hasAll = [...set2].every(item => set1.has(item));
    if (hasAll && set1.size === set2.size) return true;
  }

  return false;
};

// 判断六合卦
const unite = (symbol: string): string => {
  const name = GUA64[symbol];

  for (const pattern of LIUHE) {
    if (name && name.includes(pattern)) {
      return '六合';
    }
  }

  return '';
};

// 伏神信息接口
export interface HiddenSpirit {
  relation: string;     // 六亲关系，如"父母"
  ganZhi: string;       // 干支，如"甲寅"
  wuxing: string;       // 五行，如"木"
  position: number;     // 在本宫卦中的位置（0-5）
  hiddenUnder: number;  // 伏在哪个爻位下（0-5）
}

// 计算伏神
export const calculateHiddenSpirits = (lines: YaoType[], sixRelations: string[]): HiddenSpirit[] => {
  // 检查六亲是否齐全
  const relationTypes = ['父母', '官鬼', '兄弟', '子孙', '妻财'];
  const existingTypes = sixRelations.map(rel => {
    // 提取六亲类型（前两个字符）
    return rel.substring(0, 2);
  });

  const missingRelations = relationTypes.filter(type => !existingTypes.includes(type));

  if (missingRelations.length === 0) {
    return [];
  }

  // 获取本宫卦的六亲配置
  const origin = seekForOrigin(lines);

  // 生成本宫卦的二进制表示（八纯卦）
  // 需要根据卦宫索引获取正确的八纯卦
  const gongBinary = origin.toString(2).padStart(3, '0');
  const gongSymbol = gongBinary + gongBinary; // 本宫卦的二进制表示

  // 获取本宫卦的纳甲配置 - 直接使用卦宫索引
  const gongNajia = [
    NAJIA[origin][0][0] + NAJIA[origin][0][1], // 内卦第1爻
    NAJIA[origin][0][0] + NAJIA[origin][0][2], // 内卦第2爻
    NAJIA[origin][0][0] + NAJIA[origin][0][3], // 内卦第3爻
    NAJIA[origin][1][0] + NAJIA[origin][1][1], // 外卦第1爻
    NAJIA[origin][1][0] + NAJIA[origin][1][2], // 外卦第2爻
    NAJIA[origin][1][0] + NAJIA[origin][1][3]  // 外卦第3爻
  ];

  // 确定卦宫五行属性（按NAJIA数组索引）
  let property = 5;
  if ([7, 6].includes(origin)) property = 0; // 乾、兑属金
  else if ([2].includes(origin)) property = 1; // 坎属水
  else if ([0, 1].includes(origin)) property = 2; // 坤、艮属土
  else if ([4, 3].includes(origin)) property = 3; // 震、巽属木
  else property = 4; // 离属火

  const hiddenSpirits: HiddenSpirit[] = [];

  // 计算本宫卦的六亲
  for (let i = 0; i < 6; i++) {
    const ganZhi = gongNajia[i];
    const diZhi = ganZhi[1];
    const earthEnum = EARTH_ENUM_MAP[diZhi as any];
    const liuqin = findReps(property, earthEnum);

    if (missingRelations.includes(liuqin)) {
      const diZhiIndex = ZHIS.indexOf(diZhi as any);
      const diZhiWuxing = XING5[ZHI5[diZhiIndex]];

      // 伏神通常伏在相同位置的飞神下
      // 但如果该位置已有相同六亲，则需要找其他位置
      let hiddenUnder = i;

      // 检查是否与当前爻位的六亲冲突，如果冲突则寻找合适的位置
      const currentRelation = sixRelations[i].substring(0, 2);
      if (currentRelation === liuqin) {
        // 寻找其他位置
        for (let j = 0; j < 6; j++) {
          if (sixRelations[j].substring(0, 2) !== liuqin) {
            hiddenUnder = j;
            break;
          }
        }
      }

      hiddenSpirits.push({
        relation: liuqin,
        ganZhi: ganZhi,
        wuxing: diZhiWuxing,
        position: i,
        hiddenUnder: hiddenUnder
      });
    }
  }

  return hiddenSpirits;
};

// 计算变卦
export const calculateChangedHexagram = (lines: YaoType[]): { name: string; lines: YaoType[]; sixRelations: string[]; worldPosition: number; responsePosition: number } | null => {
  // 检查是否有动爻
  const hasChanging = lines.some(line => line.includes('changing'));
  if (!hasChanging) return null;

  // 生成变卦
  const changedLines = lines.map(line => {
    if (line === 'yang_changing') return 'yin';
    if (line === 'yin_changing') return 'yang';
    return line;
  });

  const changedBinary = changedLines.map(line => {
    const isYang = line === 'yang';
    return isYang ? '1' : '0';
  }).join('');

  const name = GUA64[changedBinary] || '乾为天';

  // 重要：变卦的六亲按照本卦的卦宫计算，不是按变卦的卦宫
  const originalOrigin = seekForOrigin(lines);

  // 确定本卦卦宫五行属性（按NAJIA数组索引）
  let originalProperty = 5;
  if ([7, 6].includes(originalOrigin)) originalProperty = 0; // 乾、兑属金
  else if ([2].includes(originalOrigin)) originalProperty = 1; // 坎属水
  else if ([0, 1].includes(originalOrigin)) originalProperty = 2; // 坤、艮属土
  else if ([4, 3].includes(originalOrigin)) originalProperty = 3; // 震、巽属木
  else originalProperty = 4; // 离属火

  const najiaGanZhi = getNajia(changedBinary);
  const sixRelations = [];

  for (let i = 0; i < 6; i++) {
    const ganZhi = najiaGanZhi[i];
    const diZhi = ganZhi[1];
    const earthEnum = EARTH_ENUM_MAP[diZhi as any];
    // 使用本卦的卦宫五行属性来计算变卦的六亲
    const liuqin = findReps(originalProperty, earthEnum);

    const diZhiIndex = ZHIS.indexOf(diZhi as any);
    const diZhiWuxing = XING5[ZHI5[diZhiIndex]];

    sixRelations.push(`${liuqin}${diZhi}${diZhiWuxing}`);
  }

  // 重要：变卦应该使用本卦的世应位置，而不是重新计算
  const originalWorldResponse = calculateWorldAndResponse(lines);

  return {
    name,
    lines: changedLines as YaoType[], // 添加变卦的爻线
    sixRelations,
    worldPosition: originalWorldResponse.worldPosition,
    responsePosition: originalWorldResponse.responsePosition
  };
};

// 计算神煞及其对应地支
export const getSpiritsWithBranches = (dayBranch: string): string[] => {
  const branchIndex = EARTHLY_BRANCHES.findIndex(branch => branch === dayBranch);
  
  if (branchIndex === -1) {
    return ['驿马(寅)', '桃花(酉)', '华盖(辰)'];
  }
  
  // 计算三合局：申子辰(水)，亥卯未(木)，寅午戌(火)，巳酉丑(金)
  let yiMa = '', taoHua = '', huaGai = '';
  
  // 根据日支确定三合局，然后计算神煞
  if ([8, 0, 4].includes(branchIndex)) { // 申(8)子(0)辰(4) - 水局
    yiMa = '寅';  // 驿马
    taoHua = '酉'; // 桃花
    huaGai = '辰'; // 华盖
  } else if ([11, 3, 7].includes(branchIndex)) { // 亥(11)卯(3)未(7) - 木局
    yiMa = '巳';
    taoHua = '子';
    huaGai = '未';
  } else if ([2, 6, 10].includes(branchIndex)) { // 寅(2)午(6)戌(10) - 火局
    yiMa = '申';
    taoHua = '卯';
    huaGai = '戌';
  } else { // 巳(5)酉(9)丑(1) - 金局
    yiMa = '亥';
    taoHua = '午';
    huaGai = '丑';
  }
  
  // 计算其他神煞
  // 羊刃：甲羊刃在卯，乙羊刃在寅，丙戊羊刃在午，丁己羊刃在巳，庚羊刃在酉，辛羊刃在申，壬羊刃在子，癸羊刃在亥
  const yangRenMap = ['卯', '寅', '午', '巳', '午', '巳', '酉', '申', '子', '亥'];
  
  // 天乙贵人：甲戊庚见牛羊，乙己见鼠猴，丙丁见猪鸡，壬癸见蛇兔，六辛见虎马
  const tianYiMap = ['丑未', '子申', '亥酉', '巳卯', '寅午'];
  
  return [
    `驿马(${yiMa})`,
    `桃花(${taoHua})`, 
    `华盖(${huaGai})`
  ];
};