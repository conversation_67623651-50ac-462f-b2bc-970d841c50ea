import React from 'react';
import { cn } from '@/lib/utils';

export type YaoType = 'yang' | 'yin' | 'yang_changing' | 'yin_changing';

interface YaoLineProps {
  type: YaoType;
  position: number;
  className?: string;
}

export const YaoLine: React.FC<YaoLineProps> = ({ type, position, className }) => {
  const isChanging = type.includes('changing');
  const isYang = type.startsWith('yang');

  return (
    <div className={cn("flex items-center justify-center h-8 mb-2", className)}>
      <div className="flex items-center space-x-2">
        <div className="relative w-24">
          {isYang ? (
            // 阳爻：一条横线
            <div className={cn("yao-line", isChanging && "changing")} />
          ) : (
            // 阴爻：两条横线
            <div className="flex space-x-2">
              <div className={cn("yao-line flex-1", isChanging && "changing")} />
              <div className={cn("yao-line flex-1", isChanging && "changing")} />
            </div>
          )}
          {isChanging && (
            <div className="absolute -right-8 top-1/2 transform -translate-y-1/2">
              <span className="text-sm font-bold text-yellow-500">○</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};